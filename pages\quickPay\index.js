Page({
  data: {
    selectedPayMethod: null, // 默认没有选中任何支付方式
    amount: '', // 输入的金额
    isProcessing: false // 防止重复提交
  },
  
  // 选择支付方式
  selectPayMethod(e) {
    const method = e.currentTarget.dataset.method;
    const currentSelected = this.data.selectedPayMethod;
    
    // 如果点击的是花呗分期，显示待开发提示
    if (method === 'huabei') {
      my.showToast({
        content: '花呗分期功能待开发',
        type: 'none',
        duration: 2000
      });
      return;
    }
    
    // 如果点击的是当前已选中的方法，则取消选中
    if (currentSelected === method) {
      this.setData({
        selectedPayMethod: null
      });
    } else {
      // 否则选中点击的方法
      this.setData({
        selectedPayMethod: method
      });
    }
  },

  // 输入金额
  onAmountInput(e) {
    this.setData({
      amount: e.detail.value
    });
  },
  
  // 验证金额格式
  validateAmount(amount) {
    // 检查是否为空
    if (!amount || amount.trim() === '') {
      return { valid: false, message: '请输入付款金额' };
    }
    
    // 检查是否为数字
    const num = parseFloat(amount);
    if (isNaN(num)) {
      return { valid: false, message: '请输入有效的数字金额' };
    }
    
    // 检查是否为正数
    if (num <= 0) {
      return { valid: false, message: '金额必须大于0' };
    }
    
    // 检查小数位数不超过2位
    if (amount.includes('.') && amount.split('.')[1].length > 2) {
      return { valid: false, message: '金额最多支持2位小数' };
    }
    
    // 检查金额不超过限额（比如10万）
    // if (num > 100000) {
    //   return { valid: false, message: '单笔付款金额不能超过10万元' };
    // }
    
    return { valid: true, message: '' };
  },
  
  // 获取支付宝授权
  getAlipayAuth() {
    return new Promise((resolve, reject) => {
      my.getAuthCode({
        scopes: ['auth_user'],
        success: (result) => {
          console.log('获取授权码成功:', result);
          resolve(result.authCode);
        },
        fail: (error) => {
          console.error('获取授权码失败:', error);
          reject(error);
        }
      });
    });
  },

  // 检查分账绑定关系
  checkBind(appid) {
    return new Promise((resolve, reject) => {
      my.request({
        url: 'https://zf.kdsjkj.com/fenrun-query/checkBind',
        method: 'GET',
        data: {
          appid: appid
        },
        header: {
          'Accept': '*/*',
          'Accept-Encoding': 'gzip, deflate, br',
          'User-Agent': 'PostmanRuntime-ApipostRuntime/1.1.0',
          'Connection': 'keep-alive',
          'Cache-Control': 'no-cache',
          'Host': 'zf.kdsjkj.com'
        },
        success: (res) => {
          console.log('检查绑定关系响应:', res.data);
          
          if (res.data && res.data.success) {
            resolve(res.data);
          } else {
            const message = res.data && res.data.message ? res.data.message : '检查绑定关系失败';
            reject(new Error(message));
          }
        },
        fail: (error) => {
          console.error('检查绑定关系请求失败:', error);
          reject(new Error('网络请求失败'));
        }
      });
    });
  },
  
  // 请求后端创建订单
  createOrderWithAuth(authCode, amount) {
    return new Promise((resolve, reject) => {
      // 获取小程序配置信息
      const extJson = my.getExtConfigSync();
      const appId = extJson && extJson.appid ? extJson.appid : ''; // 修改这里，使用 appid 而不是 appId
      
      // 检查是否获取到 appId
      if (!appId) {
        my.showToast({
          type: 'fail',
          content: '无法获取小程序appid，请联系管理员',
          duration: 6000
        });
        reject(new Error('未获取到 appId'));
        return;
      }
      
      // 构建请求参数 - 使用传入的金额
      const requestData = {
        authCode: authCode,
        amount: amount, // 使用自定义金额
        subject: "测试商品订单",
        appId: appId // 添加 appId 到请求参数
      };
      
      console.log('发送授权请求:', requestData);
      
      // 发送请求到后端
      my.request({
        url: 'https://zf.kdsjkj.com/api/alipay/auth',
        method: 'POST',
        data: requestData,
        header: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        success: (res) => {
          console.log('后端响应:', res.data);
          
          if (res.data && res.data.success) {
            resolve({
              userId: res.data.userId,
              outTradeNo: res.data.outTradeNo,
              tradeNo: res.data.tradeNo,
              orderInfo: res.data.orderInfo,
              amount: res.data.amount,
              subject: res.data.subject
            });
          } else {
            reject(new Error(res.data ? res.data.message : '创建订单失败'));
          }
        },
        fail: (error) => {
          console.error('请求失败:', error);
          reject(error);
        }
      });
    });
  },
  
  // 唤醒支付宝收银台
  openAlipayPay(tradeNo) {
    console.log('准备唤醒收银台，交易号:', tradeNo);
    
    my.tradePay({
      // 调用统一收单交易创建接口（alipay.trade.create），获得返回字段支付宝交易号 trade_no
      tradeNO: tradeNo,
      success: (res) => {
        console.log('支付成功:', res);
        my.showToast({
          content: '支付成功！',
          type: 'success',
          duration: 2000
        });
        
        // 支付成功后重置表单
        this.setData({
          amount: '',
          selectedPayMethod: null,
          isProcessing: false
        });
        
        // 可以在这里添加跳转到成功页面的逻辑
        // my.navigateTo({
        //   url: '/pages/paySuccess/index?tradeNo=' + tradeNo
        // });
      },
      fail: (error) => {
        console.error('调用 my.tradePay 失败:', JSON.stringify(error));
        
        // 处理不同的支付失败情况
        let errorMessage = '支付失败';
        if (error.resultCode) {
          switch (error.resultCode) {
            case '6001':
              errorMessage = '用户取消支付';
              break;
            case '6002':
              errorMessage = '网络连接出错';
              break;
            case '4000':
              errorMessage = '系统异常';
              break;
            default:
              errorMessage = '支付失败，错误码: ' + error.resultCode;
          }
        }
        
        my.showToast({
          content: errorMessage,
          type: 'none',
          duration: 3000
        });
        
        this.setData({
          isProcessing: false
        });
      }
    });
  },
  
  // 提交订单
  async submitOrder() {
    // 防止重复提交
    if (this.data.isProcessing) {
      return;
    }
    
    const { amount, selectedPayMethod } = this.data;
    
    // 验证金额
    const amountValidation = this.validateAmount(amount);
    if (!amountValidation.valid) {
      my.showToast({
        content: amountValidation.message,
        type: 'none',
        duration: 2000
      });
      return;
    }
    
    // 验证是否选择了支付方式
    if (!selectedPayMethod) {
      my.showToast({
        content: '请选择支付方式',
        type: 'none',
        duration: 2000
      });
      return;
    }
    
    // 只支持支付宝支付
    if (selectedPayMethod !== 'alipay') {
      my.showToast({
        content: '当前仅支持支付宝支付',
        type: 'none',
        duration: 2000
      });
      return;
    }
    
    // 显示确认对话框
    my.confirm({
      title: '确认付款',
      content: `确认支付￥${amount}元？`,
      confirmButtonText: '确认支付',
      cancelButtonText: '取消',
      success: async (result) => {
        if (result.confirm) {
          // 设置处理状态
          this.setData({
            isProcessing: true
          });
          
          my.showLoading({
            content: '正在处理...'
          });
          
          try {
            // 1. 获取小程序配置信息
            const extJson = my.getExtConfigSync();
            const appId = extJson && extJson.appid ? extJson.appid : '';
            
            // 检查是否获取到 appId
            if (!appId) {
              my.showToast({
                content: '小程序模板还没上架，请联系管理员',
                type: 'fail',
                duration: 6000
              });
              this.setData({
                isProcessing: false
              });
              my.hideLoading();
              return;
            }
            
            // 2. 检查分账绑定关系
            console.log('步骤2: 检查分账绑定关系');
            await this.checkBind(appId);
            console.log('分账绑定关系检查成功');
            
            // 3. 获取支付宝授权
            console.log('步骤3: 获取支付宝授权');
            const authCode = await this.getAlipayAuth();
            console.log('授权码获取成功:', authCode);
            
            // 4. 发送授权码到后端，创建订单（使用用户输入的金额）
            console.log('步骤4: 发送授权码到后端创建订单，金额:', amount);
            const orderResult = await this.createOrderWithAuth(authCode, amount);
            console.log('订单创建成功:', orderResult);
            
            my.hideLoading();
            
            // 5. 使用获取的 trade_no 唤醒收银台
            console.log('步骤5: 唤醒支付宝收银台');
            this.openAlipayPay(orderResult.tradeNo);
            
          } catch (error) {
            console.error('支付流程异常:', error);
            my.hideLoading();
            
            // 如果是 appId 相关的错误，直接显示错误信息
            if (error.message === '未获取到 appId') {
              // 不显示额外提示，因为createOrderWithAuth中已经显示了
              this.setData({
                isProcessing: false
              });
              return;
            }
            
            // 如果是检查绑定关系失败，显示具体错误信息
            if (error.message && error.message.includes('绑定')) {
              my.showToast({
                content: error.message,
                type: 'fail',
                duration: 3000
              });
              this.setData({
                isProcessing: false
              });
              return;
            }
            
            let errorMessage = '支付处理失败';
            if (error.message) {
              errorMessage = error.message;
            } else if (error.error && error.error.message) {
              errorMessage = error.error.message;
            }
            
            my.showToast({
              content: errorMessage,
              type: 'none',
              duration: 3000
            });
            
            this.setData({
              isProcessing: false
            });
          }
        } else {
          // 用户取消支付
          my.showToast({
            content: '取消支付',
            type: 'none',
            duration: 2000
          });
        }
      }
    });
  },

  onLoad(query) {
    // 页面加载
    console.info(`Page onLoad with query: ${JSON.stringify(query)}`);
  },
  onReady() {
    // 页面加载完成
  },
  onShow() {
    // 页面显示
  },
  onHide() {
    // 页面隐藏
  },
  onUnload() {
    // 页面被关闭
  },
  onTitleClick() {
    // 标题被点击
  },
  onPullDownRefresh() {
    // 页面被下拉
  },
  onReachBottom() {
    // 页面被拉到底部
  },
  onShareAppMessage() {
    // 返回自定义分享信息
    return {
      title: 'My App',
      desc: 'My App description',
      path: 'pages/index/index',
    };
  },
});
