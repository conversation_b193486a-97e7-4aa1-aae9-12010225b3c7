<view class="container">
  <!-- 顶部 Banner -->
  <swiper class="banner" autoplay="true" interval="3000" circular="true">
    <swiper-item a:if="{{carouselList.length > 0}}" a:for="{{carouselList}}" key="id">
      <image src="{{item.carouselUrl}}" mode="widthFix" />
    </swiper-item>
    <swiper-item a:if="{{carouselList.length === 0}}">
      <image src="/assets/banner.png" mode="widthFix" />
    </swiper-item>
    <swiper-item a:if="{{carouselList.length === 0}}">
      <image src="/assets/banner.png" mode="widthFix" />
    </swiper-item>
    <swiper-item a:if="{{carouselList.length === 0}}">
      <image src="/assets/banner.png" mode="widthFix" />
    </swiper-item>
  </swiper>

  <!-- 新闻简报 -->
  <view class="news-bar">
    <text class="news-title">新闻简报</text>
    <text class="news-content">网上商城正式上线啦！！</text>
  </view>

  <!-- 快捷按钮区 -->
  <view class="quick-actions">
    <view class="action-item" onTap="quickPay">
      <!-- <view class="action-item" > -->
      <icon type="success_no_circle" size="32" color="#FF6F3D" />
      <text>快速买单</text>
    </view>
    <view class="action-item" onTap="subscribe">
      <icon type="success" size="32" color="#FFC300" />
      <text>秒杀订阅</text>
    </view>

  </view>

  <!-- 商品选择器 -->
  <view class="product-selector" a:if="{{productList.length > 1}}">
    <text class="selector-title">选择商品：</text>
    <view class="product-options">
      <view class="product-option {{currentProduct.id === item.id ? 'active' : ''}}" 
            a:for="{{productList}}" 
            key="id" 
            onTap="selectProduct" 
            data-product="{{item}}">
        <image class="option-img" src="{{item.productImageUrl}}" mode="aspectFill" />
        <text class="option-name">{{item.productName}}</text>
        <text class="option-price">￥{{item.productPrice}}</text>
      </view>
    </view>
  </view>

  <!-- 商品卡片 -->
  <view class="product-card" a:if="{{currentProduct}}">
    <image class="product-img" src="{{currentProduct.productImageUrl}}" mode="aspectFill" />
    <view class="product-info">
      <text class="product-title">{{currentProduct.productName}}</text>
      <!-- <view class="product-tags">
        <text class="tag">可自提</text>
        <text class="tag" a:if="{{currentProduct.stockQuantity > 0}}">有库存</text>
        <text class="tag" a:if="{{currentProduct.stockQuantity <= 0}}">无库存</text>
      </view> -->
      <view class="product-bottom">
        <text class="product-price">￥{{price}}元</text>
        <view class="product-qty">
          <image src="/assets/icon-minus.svg" class="qty-svg" mode="aspectFit" onTap="subCount" />
          <text class="qty-num">{{count}}</text>
          <image src="/assets/icon-plus.svg" class="qty-svg" mode="aspectFit" onTap="addCount" />
        </view>
      </view>
    </view>
  </view>
  
  <!-- 默认商品卡片 -->
  <view class="product-card" a:if="{{!currentProduct}}">
    <image class="product-img" src="https://pic.616pic.com/ys_bnew_img/00/04/18/bJ2fu9RMld.jpg" mode="aspectFill" />
    <view class="product-info">
      <text class="product-title">手机</text>
      <view class="product-tags">
        <text class="tag">可自提</text>
      </view>
      <view class="product-bottom">
        <text class="product-price">￥{{price}}元</text>
        <view class="product-qty">
          <image src="/assets/icon-minus.svg" class="qty-svg" mode="aspectFit" onTap="subCount" />
          <text class="qty-num">{{count}}</text>
          <image src="/assets/icon-plus.svg" class="qty-svg" mode="aspectFit" onTap="addCount" />
        </view>
      </view>
    </view>
  </view>

  <!-- 底部购物车栏 -->
  <view class="cart-bar">
    <image src="https://tse1-mm.cn.bing.net/th/id/OIP-C.OVGXn6Pv8SNqdVLXjFpYLgHaHa?w=201&h=201&c=7&r=0&o=7&dpr=1.3&pid=1.7&rm=3" class="cart-icon" mode="aspectFit" />
    <text class="cart-total">合计：￥{{total}}元</text>
    <button class="checkout-btn" onTap="showAuthCode">结算</button>
  </view>
</view>
