.container {
  background: #f7f7f7;
  min-height: 100vh;
  padding-bottom: 80px;
}

.banner {
  width: 100%;
  height: 150px;
  border-radius: 16px;
  margin: 16px 0 12px 0;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(0,0,0,0.08);
  position: relative;
}
.banner swiper,
.banner swiper-item,
.banner image {
  width: 100%;
  height: 100%;
  display: block;
  border-radius: 16px;
}

/* 轮播点样式 */
.banner .ant-swiper-dots,
.banner .swiper-dots {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 8px;
  display: flex;
  justify-content: center;
  z-index: 10;
}
.banner .ant-swiper-dot,
.banner .swiper-dot {
  width: 8px;
  height: 8px;
  background: #fff;
  opacity: 0.7;
  border-radius: 50%;
  margin: 0 3px;
  transition: all 0.3s;
}
.banner .ant-swiper-dot-active,
.banner .swiper-dot-active {
  width: 18px;
  background: #ff6f3d;
  opacity: 1;
}

.news-bar {
  display: flex;
  align-items: center;
  padding: 0 16px;
  margin-bottom: 12px;
}
.news-title {
  color: #007aff;
  font-weight: bold;
  margin-right: 8px;
}
.news-content {
  color: #333;
  font-size: 14px;
}

.quick-actions {
  display: flex;
  justify-content: space-around;
  margin: 12px 0 18px 0;
}
.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 14px;
  color: #666;
}

.product-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
  margin: 0 16px 18px 16px;
  padding: 16px;
  display: flex;
  align-items: center;
}
.product-img {
  width: 90px;
  height: 90px;
  border-radius: 10px;
  margin-right: 16px;
  background: #eee;
}
.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 90px;
}
.product-title {
  font-size: 16px;
  font-weight: bold;
  color: #222;
  margin-bottom: 4px;
}
.product-tags {
  margin-bottom: 8px;
}
.tag {
  font-size: 12px;
  color: #ff6f3d;
  background: #fff3e6;
  border-radius: 6px;
  padding: 2px 8px;
  margin-right: 6px;
}
.product-bottom {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.product-price {
  color: #ff6f3d;
  font-size: 16px;
  font-weight: bold;
}
.product-qty {
  display: flex;
  align-items: center;
  justify-content: center;
}

.qty-svg {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background: #fff7f0;
  box-shadow: 0 2px 8px rgba(255,111,61,0.10);
  margin: 0 6px;
  transition: box-shadow 0.2s, transform 0.2s;
  vertical-align: middle;
}
.qty-svg:active {
  box-shadow: 0 1px 2px rgba(255,111,61,0.18);
  transform: scale(0.92);
}
.qty-num {
  font-size: 18px;
  font-weight: bold;
  color: #222;
  margin: 0 4px;
  min-width: 20px;
  text-align: center;
  display: inline-block;
}

.cart-bar {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  background: #fff;
  box-shadow: 0 -2px 16px rgba(0,0,0,0.10);
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 12px 18px 12px 12px;
  z-index: 99;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  min-height: 56px;
}
.cart-bar icon {
  margin-right: 10px;
  font-size: 28px;
  color: #ff6f3d;
  background: #fff7f0;
  border-radius: 50%;
  padding: 4px;
  box-shadow: 0 2px 8px rgba(255,111,61,0.08);
}
.cart-total {
  flex: 1;
  color: #222;
  font-size: 18px;
  margin-left: 10px;
  font-weight: bold;
}
.checkout-btn {
  background: linear-gradient(90deg, #ff8a3d, #ff6f3d);
  color: #fff;
  border: none;
  border-radius: 22px;
  padding: 10px 36px;
  font-size: 18px;
  font-weight: bold;
  margin-right: 8px;
  box-shadow: 0 2px 8px rgba(255,111,61,0.10);
  letter-spacing: 2px;
}

.cart-icon {
  width: 28px;
  height: 28px;
  margin-right: 10px;
  background: #fff7f0;
  border-radius: 50%;
  padding: 4px;
  box-shadow: 0 2px 8px rgba(255,111,61,0.08);
}

.auth-code-area {
  margin: 20rpx;
  padding: 20rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
}

.auth-code-label {
  color: #666;
  font-size: 28rpx;
  margin-right: 10rpx;
}

.auth-code-value {
  color: #333;
  font-size: 28rpx;
  flex: 1;
  word-break: break-all;
}

.checkout-btn:active {
  transform: scale(0.96);
  box-shadow: 0 2px 8px rgba(255,111,61,0.25);
}

/* 商品选择器样式 */
.product-selector {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
  margin: 0 16px 12px 16px;
  padding: 16px;
}
.selector-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 12px;
  display: block;
}
.product-options {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}
.product-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  border: 2px solid #f0f0f0;
  border-radius: 8px;
  background: #fafafa;
  transition: all 0.3s;
  min-width: 80px;
}
.product-option.active {
  border-color: #ff6f3d;
  background: #fff7f0;
}
.option-img {
  width: 60px;
  height: 60px;
  border-radius: 6px;
  margin-bottom: 8px;
  background: #eee;
}
.option-name {
  font-size: 12px;
  color: #333;
  text-align: center;
  margin-bottom: 4px;
  max-width: 80px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.option-price {
  font-size: 12px;
  color: #ff6f3d;
  font-weight: bold;
} 