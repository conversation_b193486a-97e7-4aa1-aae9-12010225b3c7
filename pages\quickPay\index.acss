.container {
  background: #f5f7fa;
  min-height: 100vh;
  padding: 0 24rpx 24rpx 24rpx;
}
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 0 16rpx 0;
}
.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #222;
}
.header-icon {
  font-size: 32rpx;
  color: #999;
}
.pay-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx 24rpx 24rpx 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx #e5e5e5;
}
.pay-title {
  font-size: 28rpx;
  color: #1677ff;
  font-weight: bold;
}
.shop-name {
  font-size: 24rpx;
  color: #666;
  margin: 12rpx 0 24rpx 0;
}
.input-area {
  display: flex;
  align-items: center;
  border: 1rpx solid #e5e5e5;
  border-radius: 8rpx;
  padding: 12rpx 16rpx;
  background: #f8f8f8;
}
.input-label {
  font-size: 24rpx;
  color: #888;
  margin-right: 16rpx;
}
.amount-input {
  flex: 1;
  font-size: 32rpx;
  border: none;
  background: transparent;
}
.pay-method-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx #e5e5e5;
}
.tab-bar {
  display: flex;
  border-bottom: 2rpx solid #f0f0f0;
  margin-bottom: 24rpx;
}
.tab {
  font-size: 26rpx;
  color: #888;
  padding: 0 32rpx 16rpx 0;
  margin-right: 32rpx;
  position: relative;
}
.tab.active {
  color: #1677ff;
  font-weight: bold;
  border-bottom: 4rpx solid #1677ff;
}
.pay-method-list {
  margin-top: 16rpx;
}
.pay-method-item {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  cursor: pointer;
}

/* 自定义radio图标样式 */
.radio-icon {
  width: 32rpx;
  height: 32rpx;
  border: 2rpx solid #d9d9d9;
  border-radius: 50%;
  margin-right: 16rpx;
  position: relative;
  transition: all 0.2s ease;
}

.radio-icon.checked {
  border-color: #1677ff;
  background-color: #1677ff;
}

.radio-icon.checked::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 12rpx;
  height: 12rpx;
  background-color: #fff;
  border-radius: 50%;
}
.pay-icon {
  width: 40rpx;
  height: 40rpx;
  margin: 0 16rpx;
}
.submit-btn {
  width: 100%;
  background: #1677ff;
  color: #fff;
  font-size: 32rpx;
  border-radius: 12rpx;
  margin: 32rpx 0 16rpx 0;
  padding: 20rpx 0;
  font-weight: bold;
}
.tips {
  font-size: 26rpx;
  color: #f00;
  margin-top: 16rpx;
  line-height: 1.8;
}
.tips text {
  display: block;
  margin-bottom: 8rpx;
}
.tips text:first-child {
  color: #f00;
  font-weight: bold;
  margin-bottom: 12rpx;
} 